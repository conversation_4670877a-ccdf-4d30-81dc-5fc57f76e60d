/**
 * VoiceAgentCoordinator - Central coordinator for voice agent services
 *
 * This class orchestrates communication between:
 * - DeepgramService (speech-to-text)
 * - GroqService (AI processing)
 * - ActionPlanExecutor (plan execution)
 *
 * Provides a unified interface for the VoiceAgentPanel to interact with
 * all voice-related services through a single coordination layer.
 */

import { actionPlanExecutor } from './ActionPlanExecutor'
import { globalContextService } from './context/GlobalContextService'
import { localContextService } from './context/LocalContextService'
import { deepgramAgentService } from './DeepgramAgentService'
import { deepgramService } from './DeepgramService'
import { geminiService } from './GeminiService'
import { groqService } from './GroqService'

export class VoiceAgentCoordinator {
  constructor () {
    // Service selection - set to true to use Agent API, false for STT-only
    this.useAgentAPI = false

    // Service instances
    this.deepgramService = this.useAgentAPI ? deepgramAgentService : deepgramService
    this.llmService = geminiService
    this.actionPlanExecutor = actionPlanExecutor

    // Context services
    this.globalContextService = globalContextService
    this.localContextService = localContextService

    // Coordinator state
    this.state = 'idle' // 'idle', 'initializing', 'listening', 'processing', 'error'
    this.currentInteraction = {
      userRequest: '',
      agentResponse: '',
      executionPlan: [],
    }

    // Performance timing
    this.timings = {
      transcriptionStart: 0,
      transcriptionEnd: 0,
      thinkingStart: 0,
      thinkingEnd: 0,
      executionStart: 0,
      executionEnd: 0,
    }

    // Speech debouncing to prevent multiple requests for partial speech
    this.speechDebounceMs = 0 // Wait time after last speech segment
    this.speechBuffer = [] // Collect multiple speech segments
    this.debounceTimer = null // Timer for debouncing

    // Event listeners for UI components
    this.listeners = {
      stateChange: [],
      interactionUpdate: [],
      error: [],
    }

    // Bind service event handlers
    this.setupServiceListeners()

    this.requestHistory = []
  }

  /**
   * Start voice agent listening
   */
  async startListening () {
    if (this.state === 'listening' || this.state === 'processing') {
      console.warn('[VoiceCoordinator] Already listening')
      return
    }

    try {
      this.setState('initializing')

      // Clear previous interaction
      this.currentInteraction = {
        userRequest: '',
        agentResponse: '',
        executionPlan: [],
      }
      this.emit('interactionUpdate', { ...this.currentInteraction })

      // Start ActionPlanExecutor if not already started
      if (!this.actionPlanExecutor.isStarted) {
        this.actionPlanExecutor.start()
      }

      // Start Deepgram service
      await this.deepgramService.startListening()

      console.log('[VoiceCoordinator] Voice agent started successfully')
    } catch (error) {
      console.error('[VoiceCoordinator] Failed to start voice agent:', error)
      this.setState('error')
      this.emit('error', error)
    }
  }

  /**
   * Stop voice agent listening
   */
  async stopListening () {
    if (this.state === 'idle') {
      return
    }

    try {
      // Stop Deepgram service
      await this.deepgramService.stopListening()

      // Clear interaction data
      this.currentInteraction = {
        userRequest: '',
        agentResponse: '',
        executionPlan: [],
      }
      this.emit('interactionUpdate', { ...this.currentInteraction })

      // Clear speech buffer and debounce timer
      this.clearSpeechBuffer()

      // Clear request history
      this.requestHistory = []

      this.setState('idle')
      console.log('[VoiceCoordinator] Voice agent stopped successfully')
    } catch (error) {
      console.error('[VoiceCoordinator] Error stopping voice agent:', error)
      this.setState('error')
      this.emit('error', error)
    }
  }

  /**
   * Replay the last user request without requiring new speech input
   */
  async replayLastUserRequest () {
    if (!this.currentInteraction.userRequest) {
      console.warn('[VoiceCoordinator] No previous user request to replay')
      return
    }

    if (this.state === 'processing') {
      console.warn('[VoiceCoordinator] Already processing, cannot replay')
      return
    }

    try {
      console.log(`[VoiceCoordinator] 🔄 Replaying last user request: "${this.currentInteraction.userRequest}"`)

      // Clear previous agent response and execution plan
      this.currentInteraction.agentResponse = ''
      this.currentInteraction.executionPlan = []
      this.emit('interactionUpdate', { ...this.currentInteraction })

      // Set processing state
      this.setState('processing')

      // Process with LLM using common method
      await this._processUserRequestWithLLM(this.currentInteraction.userRequest, { type: 'replay' })

      console.log('[VoiceCoordinator] 🔄 Replay request sent to LLM successfully')
    } catch (error) {
      console.error('[VoiceCoordinator] Failed to replay last user request:', error)
      this.handleServiceError(error)
    }
  }

  /**
   * Get current availability status
   */
  isAvailable () {
    return this.deepgramService.isAvailable() && this.llmService.isAvailable()
  }

  /**
   * Get current state
   */
  getState () {
    return this.state
  }

  /**
   * Get current interaction data
   */
  getCurrentInteraction () {
    return { ...this.currentInteraction }
  }

  /**
   * Set coordinator state and emit change event
   */
  setState (newState) {
    const oldState = this.state
    this.state = newState
    console.log(`[VoiceCoordinator] State changed: ${oldState} -> ${newState}`)
    this.emit('stateChange', { oldState, newState })
  }

  /**
   * Setup event listeners for all services
   */
  setupServiceListeners () {
    // Deepgram service events
    this.deepgramService.addEventListener('stateChange', this.handleDeepgramStateChange.bind(this))
    this.deepgramService.addEventListener('transcription', this.handleTranscription.bind(this))
    this.deepgramService.addEventListener('transcribingStart', this.handleTranscribingStart.bind(this))
    this.deepgramService.addEventListener('error', this.handleServiceError.bind(this))

    // LLM service events
    this.llmService.addEventListener('stateChange', this.handleLLMStateChange.bind(this))
    this.llmService.addEventListener('response', this.handleLLMResponse.bind(this))
    this.llmService.addEventListener('error', this.handleServiceError.bind(this))

    // ActionPlanExecutor events
    this.actionPlanExecutor.addEventListener('planComplete', this.handlePlanComplete.bind(this))
    this.actionPlanExecutor.addEventListener('error', this.handleServiceError.bind(this))
  }

  /**
   * Handle Deepgram state changes
   */
  handleDeepgramStateChange (data) {
    console.log('[VoiceCoordinator] Deepgram state:', data.newState)

    switch (data.newState) {
      case 'listening': {
        this.setState('listening')
        break
      }
      case 'error': {
        this.setState('error')
        break
      }
      case 'idle': {
        if (this.state !== 'processing') {
          this.setState('idle')
        }
        break
      }
    }
  }

  /**
   * Handle LLM state changes
   */
  handleLLMStateChange (data) {
    console.log('[VoiceCoordinator] LLM state:', data.newState)

    if (data.newState === 'processing') {
      this.setState('processing')
    } else if (data.newState === 'idle' && this.deepgramService.getState() === 'listening') {
      this.setState('listening')
    }
  }

  /**
   * Handle when speech is detected and transcription starts
   */
  handleTranscribingStart () {
    // Only reset timing and clear responses if this is a truly new interaction
    // (not just continuing speech detection)
    if (this.speechBuffer.length === 0) {
      this.timings.transcriptionStart = Date.now()
      console.warn('[VoiceCoordinator] 🎤 SPEECH DETECTED - starting new transcription timing...')

      // @NOTE: commented this part because it cleared the agent response & execution plan too early.
      // Clear previous agent response and execution plan for new interaction
      // this.currentInteraction.agentResponse = ''
      // this.currentInteraction.executionPlan = []
      // this.emit('interactionUpdate', { ...this.currentInteraction })
    } else {
      console.warn('[VoiceCoordinator] 🎤 SPEECH DETECTED - continuing current interaction...')
    }
  }

  /**
   * Handle transcription from Deepgram
   */
  async handleTranscription (result) {
    if (this.processing) {
      return
    }
    this.timings.transcriptionEnd = Date.now()

    // Only calculate transcription time if speech was detected
    const transcriptionTime = this.timings.transcriptionStart > 0
      ? this.timings.transcriptionEnd - this.timings.transcriptionStart
      : 0

    console.log(`[VoiceCoordinator] 🎤 Transcription received (${transcriptionTime > 0 ? transcriptionTime + 'ms' : 'timing not available'}):`, result.text)

    // Add transcription to speech buffer and start debouncing
    this.addToSpeechBuffer(result.text)
  }

  /**
   * Add transcription to speech buffer and manage debouncing
   */
  addToSpeechBuffer (text) {
    // Clear agent response and execution plan when first transcription arrives
    if (this.speechBuffer.length === 0) {
      this.currentInteraction.agentResponse = ''
      this.currentInteraction.executionPlan = []
    }

    // Add new text to buffer
    this.speechBuffer.push(text.trim())

    // Update UI with current buffered text
    const bufferedText = this.speechBuffer.join(' ')
    this.currentInteraction.userRequest = bufferedText
    this.emit('interactionUpdate', { ...this.currentInteraction })

    console.log(`[VoiceCoordinator] 📝 Added to speech buffer: "${text}" (total: "${bufferedText}")`)

    // Clear existing timer and start new one
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer)
    }

    // Start debounce timer
    this.debounceTimer = setTimeout(() => {
      void this.processBufferedSpeech()
    }, this.speechDebounceMs)

    console.log(`[VoiceCoordinator] ⏱️ Debounce timer started (${this.speechDebounceMs}ms)`)
  }

  /**
   * Process buffered speech and send to Gemini
   */
  async processBufferedSpeech () {
    try {
      if (this.speechBuffer.length === 0 || this.processing) {
        return
      }
      this.processing = true

      const completeText = this.speechBuffer.join(' ').trim()
      console.log(`[VoiceCoordinator] 🎯 Processing complete speech: "${completeText}"`)

      // Clear the buffer
      this.clearSpeechBuffer()

      // Process with LLM using common method
      await this._processUserRequestWithLLM(completeText, { type: 'speech' })

      // Add request to history
      this.requestHistory.push({ input: completeText })

      console.log('[VoiceCoordinator] 🤖 LLM response received')
    } catch (error) {
      console.error('[VoiceCoordinator] Failed to process transcription:', error)
      this.handleServiceError(error)
    } finally {
      this.processing = false
    }
  }

  /**
   * Clear speech buffer and timer
   */
  clearSpeechBuffer () {
    this.speechBuffer = []
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer)
      this.debounceTimer = null
    }
  }

  /**
   * Private method to handle common LLM processing logic
   * @param {string} userText - The user request text to process
   * @param {object} context - Additional context for logging/behavior differences
   */
  async _processUserRequestWithLLM (userText, context = {}) {
    // Start thinking phase timing
    this.timings.thinkingStart = Date.now()

    // Context-aware logging
    const logMessage = context.type === 'replay'
      ? '[VoiceCoordinator] 🧠 Starting LLM thinking phase for replay...'
      : '[VoiceCoordinator] 🧠 Starting LLM thinking phase...'
    console.log(logMessage)

    // Get contexts for LLM processing
    const globalContext = this.globalContextService.getContextForLLM()
    const localContext = this.localContextService.getContextForLLM()

    // Process with LLM
    await this.llmService.processUserRequest(
      userText,
      this.requestHistory,
      window.location.pathname + window.location.search,
      globalContext,
      localContext,
    )
  }

  /**
   * Handle response from LLM
   */
  handleLLMResponse (response) {
    this.timings.thinkingEnd = Date.now()
    const thinkingTime = this.timings.thinkingEnd - this.timings.thinkingStart

    console.log(`[VoiceCoordinator] 💬 LLM thinking complete (${thinkingTime}ms):`, response.text)

    // Update interaction data
    this.currentInteraction.agentResponse = response.text
    this.currentInteraction.executionPlan = response.executionPlan || []
    this.emit('interactionUpdate', { ...this.currentInteraction })

    // Execute plan if present
    if (response.executionPlan && response.executionPlan.length > 0) {
      this.timings.executionStart = Date.now()
      console.log('[VoiceCoordinator] 🎯 Starting plan execution:', response.executionPlan)

      const planId = this.actionPlanExecutor.queuePlan(response.executionPlan, 'high')
      console.log(`[VoiceCoordinator] ✅ Plan queued with ID: ${planId}`)

      // Note: We'll measure execution completion when we get feedback from ActionPlanExecutor
    } else {
      // No execution needed, log total interaction time
      this.logTotalInteractionTime()
    }
  }

  /**
   * Handle plan execution completion
   */
  handlePlanComplete (data) {
    this.timings.executionEnd = Date.now()
    const executionTime = this.timings.executionEnd - this.timings.executionStart

    console.log(`[VoiceCoordinator] 🎯 Plan execution complete (${executionTime}ms):`, data.result)

    // Log comprehensive timing summary
    this.logTotalInteractionTime()

    const executedPlan = data.result.log.map(({ step }) => step)
    this.requestHistory.push({ output: {
      description: 'Completed execution plan',
      execution_plan: executedPlan,
    } })
  }

  /**
   * Log comprehensive timing information for the interaction
   */
  logTotalInteractionTime () {
    const transcriptionTime = this.timings.transcriptionStart > 0 && this.timings.transcriptionEnd > 0
      ? this.timings.transcriptionEnd - this.timings.transcriptionStart
      : 0
    const thinkingTime = this.timings.thinkingEnd - this.timings.thinkingStart
    const executionTime = this.timings.executionEnd > 0
      ? this.timings.executionEnd - this.timings.executionStart
      : 0

    // Calculate total time from the earliest valid start time
    const startTime = this.timings.transcriptionStart > 0
      ? this.timings.transcriptionStart
      : this.timings.thinkingStart
    const totalTime = (this.timings.executionEnd > 0 ? this.timings.executionEnd : this.timings.thinkingEnd) - startTime

    console.log('📊 [VoiceCoordinator] Interaction Performance Summary:')
    console.log(`   🎤 Transcription: ${transcriptionTime > 0 ? transcriptionTime + 'ms' : 'not measured'}`)
    console.log(`   🧠 Thinking:      ${thinkingTime}ms`)
    if (executionTime > 0) {
      console.log(`   🎯 Execution:     ${executionTime}ms`)
    }
    console.log(`   ⏱️ Total:         ${totalTime}ms`)
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
  }

  /**
   * Handle errors from any service
   */
  handleServiceError (error) {
    console.error('[VoiceCoordinator] Service error:', [error?.data, error?.response, error?.error, error?.info, JSON.stringify(error, Object.getOwnPropertyNames(error))])
    this.setState('error')
    this.emit('error', { message: error?.error?.error?.message || 'Failed to process your command please try again' })
  }

  /**
   * Add event listener
   */
  addEventListener (event, callback) {
    if (this.listeners[event]) {
      this.listeners[event].push(callback)
    }
  }

  /**
   * Remove event listener
   */
  removeEventListener (event, callback) {
    if (this.listeners[event]) {
      const index = this.listeners[event].indexOf(callback)
      if (index !== -1) {
        this.listeners[event].splice(index, 1)
      }
    }
  }

  /**
   * Emit event to all listeners
   */
  emit (event, data) {
    if (this.listeners[event]) {
      for (const callback of this.listeners[event]) {
        try {
          callback(data)
        } catch (error) {
          console.error(`[VoiceCoordinator] Error in ${event} listener:`, error)
        }
      }
    }
  }

  /**
   * Cleanup resources
   */
  destroy () {
    // Stop all services
    this.stopListening()

    // Clear speech buffer and debounce timer
    this.clearSpeechBuffer()

    // Clear event listeners
    for (const event of Object.keys(this.listeners)) {
      this.listeners[event] = []
    }

    console.log('[VoiceCoordinator] Coordinator destroyed')
  }
}

// Export singleton instance
export const voiceAgentCoordinator = new VoiceAgentCoordinator()
